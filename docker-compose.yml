version: '3.8'

services:
  frontend:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./client:/app
    environment:
      - HOST=0.0.0.0
      - PORT=3000
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0 --port 3000"

  backend:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "3001:3001"
    volumes:
      - ./server:/app
    command: sh -c "npm install && npm run dev"
