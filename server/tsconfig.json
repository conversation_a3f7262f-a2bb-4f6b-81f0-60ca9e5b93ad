{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["../shared/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}