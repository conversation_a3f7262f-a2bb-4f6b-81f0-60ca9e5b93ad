{"name": "education-journey-server", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "echo 'Server linting skipped for now'"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "firebase-admin": "^12.0.0", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.10.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.18", "@types/node": "^22.5.5", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/body-parser": "^1.19.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "tsx": "^4.7.0", "typescript": "^5.5.3"}}