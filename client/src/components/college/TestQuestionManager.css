/* Custom styles for the TestQuestionManager component */

/* Table container with fixed height and scrolling */
.table-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
}

/* Ensure table has fixed layout for better column width control */
.test-question-table {
  table-layout: fixed !important;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* Fix table header to stay visible during scroll */
.test-question-table thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Improve cell text handling */
.test-question-table td,
.test-question-table th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid #e2e8f0;
}

/* Question text cell specific styles */
.question-text-cell {
  position: relative;
  max-width: 100%;
}

/* Truncated text with hover effect */
.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: block;
}

/* Show full text on hover */
.truncate-text:hover {
  white-space: normal;
  overflow: visible;
  position: relative;
  z-index: 10;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 4px;
  transition: all 0.2s ease;
}

/* Ensure the dialog is wide enough and has proper height */
.question-dialog {
  max-width: 90vw !important;
  width: 900px !important;
  max-height: 90vh !important;
}

/* Dialog content scrolling */
.question-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .truncate-text:hover {
    max-width: 90vw;
  }

  .table-container {
    max-height: 400px;
  }
}
