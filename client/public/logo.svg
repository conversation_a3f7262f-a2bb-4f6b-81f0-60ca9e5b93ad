<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="#7C3AED" stroke="#5B21B6" stroke-width="2"/>
  
  <!-- Graduation cap -->
  <g transform="translate(32, 32)">
    <!-- Cap base -->
    <ellipse cx="0" cy="-8" rx="18" ry="6" fill="#FFF"/>
    <!-- Cap top -->
    <ellipse cx="0" cy="-12" rx="18" ry="6" fill="#F3F4F6"/>
    <!-- Cap button -->
    <circle cx="0" cy="-12" r="2" fill="#7C3AED"/>
    <!-- Tassel -->
    <line x1="12" y1="-12" x2="16" y2="-8" stroke="#FCD34D" stroke-width="2" stroke-linecap="round"/>
    <circle cx="16" cy="-8" r="1.5" fill="#FCD34D"/>
  </g>
  
  <!-- Book -->
  <g transform="translate(32, 45)">
    <rect x="-8" y="-4" width="16" height="8" rx="1" fill="#FFF"/>
    <rect x="-7" y="-3" width="14" height="6" rx="0.5" fill="#E5E7EB"/>
    <line x1="-4" y1="-2" x2="4" y2="-2" stroke="#7C3AED" stroke-width="1"/>
    <line x1="-4" y1="0" x2="4" y2="0" stroke="#7C3AED" stroke-width="1"/>
    <line x1="-4" y1="2" x2="2" y2="2" stroke="#7C3AED" stroke-width="1"/>
  </g>
</svg>
