{"name": "education-journey-launchpad", "private": true, "version": "0.0.0", "workspaces": ["client", "server", "shared"], "scripts": {"dev": "concurrently \"npm run client:dev\" \"npm run server:dev\"", "client:dev": "cd client && npm run dev", "server:dev": "cd server && npm run dev", "build": "npm run client:build && npm run server:build", "client:build": "cd client && npm run build", "server:build": "cd server && npm run build", "lint": "npm run client:lint && npm run server:lint", "client:lint": "cd client && npm run lint", "server:lint": "cd server && npm run lint", "start": "concurrently \"npm run client:dev\" \"npm run server:dev\"", "install:all": "npm install && cd client && npm install && cd ../server && npm install && cd ../shared && npm install"}, "dependencies": {"concurrently": "^8.2.2"}, "devDependencies": {"@types/node": "^22.5.5", "typescript": "^5.5.3"}}