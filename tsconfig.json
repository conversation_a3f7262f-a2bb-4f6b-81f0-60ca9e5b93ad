{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@shared/*": ["./shared/src/*"], "@config/*": ["./config/*"]}}, "references": [{"path": "./client"}, {"path": "./server"}, {"path": "./shared"}], "files": [], "include": []}